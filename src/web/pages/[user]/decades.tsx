import classNames from "classnames";
import * as React from "react";

import { useAuthentication } from "../../components/Authentication.js";
import Head from "../../components/Head.js";
import { pluralize } from "../../components/I18n.js";
import Join from "../../components/Join.js";
import { Rewrite, useRouter } from "../../components/Router.js";
import TabControl from "../../components/TabControl.js";
import { graphql, useQuery } from "../../graphql/client.js";

const GetProfilePageByDecades = graphql(/* GraphQL */ `
  query GetProfilePageByDecades($userSlug: ID, $kinopoiskId: ID) {
    movieViewsByDecade(userSlug: $userSlug, kinopoiskId: $kinopoiskId) {
      decade {
        id
        label
      }
      bestMarksCount
      goodMarksCount
      views {
        mark
        movie {
          id
          kinopoiskId
          directors {
            fullName
          }
          slug
          title
          goodMarksPercentage
          bestMarksPercentage
          topPositionAllTime
          year
        }
      }
    }
    movieViewStats(userSlug: $userSlug, kinopoiskId: $kinopoiskId) {
      totalCount
      marksCount
    }
    movieViewStatsByDecade(userSlug: $userSlug, kinopoiskId: $kinopoiskId) {
      decade {
        id
        label
      }
      marksCount
    }
    movieViewStatsByDirector(
      userSlug: $userSlug
      kinopoiskId: $kinopoiskId
      sort: MARKS_OLYMPLIC_COUNT
    ) {
      person {
        id
        lastName
        slug
      }
      marksCount
    }
    movieViewStatsByGenre(
      userSlug: $userSlug
      kinopoiskId: $kinopoiskId
      sort: MARKS_OLYMPLIC_COUNT
    ) {
      genre {
        id
        label
        slug
      }
      marksCount
    }
    user(userSlug: $userSlug) {
      name
    }
  }
`);

const ProfilePageByDecades: React.FC = () => {
  const router = useRouter();
  const authentication = useAuthentication();

  // Helper function to generate the correct route pattern based on current route
  const getUserRoute = (subPath = "") => {
    if (router.pattern === "/user/by-kinopoisk-id/:kinopoiskId/decades") {
      return `/user/by-kinopoisk-id/:kinopoiskId${subPath}` as keyof PatternParamsMap;
    }
    return `/:user${subPath}` as keyof PatternParamsMap;
  };

  const getUserParams = () => {
    if (router.pattern === "/user/by-kinopoisk-id/:kinopoiskId/decades") {
      return {
        kinopoiskId: (router.params as { kinopoiskId: string }).kinopoiskId,
      };
    }
    return { user: (router.params as { user: string }).user };
  };

  if (
    router.pattern !== "/:user/decades" &&
    router.pattern !== "/user/by-kinopoisk-id/:kinopoiskId/decades"
  ) {
    throw new Error("Unexpected pattern");
  }

  const { data } = useQuery(GetProfilePageByDecades, {
    userSlug:
      router.pattern === "/:user/decades" ? router.params.user : undefined,
    kinopoiskId:
      router.pattern === "/user/by-kinopoisk-id/:kinopoiskId/decades"
        ? router.params.kinopoiskId
        : undefined,
  });

  if (!data) {
    return null;
  }

  if (!data.user) {
    return <Rewrite to={{ pathname: "/404" }} />;
  }

  const isMyProfile = data.user.name === authentication.user.name;

  return (
    <main className="container mx-auto mb-10 px-13 text-lg antialiased">
      <Head>
        <title>{isMyProfile ? "Мой профиль" : data.user.name} – Зырь</title>
      </Head>
      <h1 className="mt-2 mb-3 text-7xl font-bold tracking-tight">
        {isMyProfile ? "Мой профиль" : data.user.name}
      </h1>
      {/* flex-row-reverse here is to show sidebar immediately, not after whole body renders */}
      <div className="flex flex-row-reverse justify-end">
        <aside className="float-right clear-both ml-10">
          <table>
            <tr>
              <td className="px-2 pb-3" colSpan={5}>
                {isMyProfile
                  ? "Статистика по моим оценкам"
                  : `Статистика по оценкам ${data.user.name}`}
              </td>
            </tr>
            <tr>
              <td />
              <td className="pl-3 text-right">
                <span className="inline-block h-[14px] w-[14px] rounded-full bg-pink-500" />
              </td>
              <td className="pl-3 text-right">
                {" "}
                <span className="inline-block h-[14px] w-[14px] rounded-full bg-indigo-500" />
              </td>
              <td className="pl-3 text-right">
                {" "}
                <span className="inline-block h-[14px] w-[14px] rounded-full bg-zinc-400" />
              </td>
              <td />
            </tr>
            <tr>
              <td>
                <span className="px-2">
                  {data.movieViewStats.totalCount}{" "}
                  {pluralize(data.movieViewStats.totalCount, {
                    one: "фильм",
                    few: "фильма",
                    many: "фильмов",
                    other: "фильмов",
                  })}
                </span>
              </td>
              <td className="pl-3 text-right">
                {data.movieViewStats.marksCount[0] || null}
              </td>
              <td className="pl-3 text-right">
                {data.movieViewStats.marksCount[1] || null}
              </td>
              <td className="pl-3 text-right">
                {data.movieViewStats.marksCount[2] +
                  data.movieViewStats.marksCount[3] || null}
              </td>
              <td>
                {" "}
                {pluralize(
                  data.movieViewStats.marksCount[2] +
                    data.movieViewStats.marksCount[3],
                  {
                    one: "фильм",
                    few: "фильма",
                    many: "фильмов",
                    other: "фильмов",
                  },
                )}
              </td>
            </tr>
            <tr>
              <td className="px-2 pt-3 pb-0.5" colSpan={3}>
                по режиссёрам:
              </td>
            </tr>
            {data.movieViewStatsByDirector.map((bucket) => (
              <tr key={bucket.person.id}>
                <td className="pb-0.5">
                  <a
                    href={
                      router.stringify("/:user/directors", {
                        user: router.params.user,
                      }) + `#${bucket.person.slug ?? bucket.person.id}`
                    }
                    className="px-2 text-indigo-800 transition-colors hover:text-indigo-500 hover:transition-none"
                  >
                    {bucket.person.lastName}
                  </a>
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[0] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[1] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[2] + bucket.marksCount[3] || null}
                </td>
                <td />
              </tr>
            ))}
            <tr>
              <td className="px-2 pt-3 pb-0.5" colSpan={3}>
                по десятилетиям:
              </td>
            </tr>
            {data.movieViewStatsByDecade.map((bucket) => (
              <tr key={bucket.decade.id}>
                <td className="pb-0.5">
                  <a
                    href={
                      router.stringify("/:user/decades", {
                        user: router.params.user,
                      }) + `#${bucket.decade.id}`
                    }
                    className="px-2 text-indigo-800 transition-colors hover:text-indigo-500 hover:transition-none"
                  >
                    {bucket.decade.label}
                  </a>
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[0] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[1] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[2] + bucket.marksCount[3] || null}
                </td>
                <td />
              </tr>
            ))}
            <tr>
              <td className="px-2 pt-3 pb-0.5" colSpan={3}>
                по жанрам:
              </td>
            </tr>
            {data.movieViewStatsByGenre.map((bucket) => (
              <tr key={bucket.genre.slug}>
                <td className="pb-0.5">
                  <a
                    href={
                      router.stringify("/:user/genres", {
                        user: router.params.user,
                      }) + `#${bucket.genre.slug}`
                    }
                    className="px-2 whitespace-nowrap text-indigo-800 transition-colors hover:text-indigo-500 hover:transition-none"
                  >
                    {bucket.genre.label}
                  </a>
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[0] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[1] || null}
                </td>
                <td className="pl-3 text-right">
                  {bucket.marksCount[2] + bucket.marksCount[3] || null}
                </td>
                <td />
              </tr>
            ))}
          </table>
        </aside>
        <div className="w-[540px]">
          <div className="mb-6">
            <TabControl
              items={[
                {
                  label: "Недавнее",
                  href: router.stringify("/:user", {
                    user: router.params.user,
                  }),
                  selected: false,
                },
                {
                  label: "Лучшее",
                  href: router.stringify("/:user/top", {
                    user: router.params.user,
                  }),
                  selected: false,
                },
                {
                  label: "Десятилетия",
                  selected: true,
                },
                {
                  label: "Жанры",
                  href: router.stringify("/:user/genres", {
                    user: router.params.user,
                  }),
                  selected: false,
                },
                {
                  label: "Режиссёры",
                  href: router.stringify("/:user/directors", {
                    user: router.params.user,
                  }),
                  selected: false,
                },
              ]}
            />
          </div>

          {data.movieViewsByDecade.map((group) => (
            <React.Fragment key={group.decade.id}>
              <h3 id={group.decade.id} className="mt-4 mb-2 text-2xl font-bold">
                {group.decade.label}{" "}
                <span className="text-xs font-medium text-zinc-500">
                  {group.bestMarksCount ? (
                    <span className="mr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-px inline h-[0.8lh] w-[0.8lh]"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" />
                      </svg>
                      {group.bestMarksCount}{" "}
                      {pluralize(group.bestMarksCount, {
                        one: "фильм",
                        few: "фильма",
                        many: "фильмов",
                        other: "фильмов",
                      })}
                    </span>
                  ) : null}
                  {group.goodMarksCount ? (
                    <span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-px inline h-[0.8lh] w-[0.8lh]"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M7 11v8a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1v-7a1 1 0 0 1 1 -1h3a4 4 0 0 0 4 -4v-1a2 2 0 0 1 4 0v5h3a2 2 0 0 1 2 2l-1 5a2 3 0 0 1 -2 2h-7a3 3 0 0 1 -3 -3" />
                      </svg>
                      {group.goodMarksCount}{" "}
                      {pluralize(group.goodMarksCount, {
                        one: "фильм",
                        few: "фильма",
                        many: "фильмов",
                        other: "фильмов",
                      })}
                    </span>
                  ) : null}
                </span>
              </h3>
              <ol className="relative space-y-2">
                {group.views.map((item) => (
                  <li key={item.movie.id}>
                    {item.mark ? (
                      <span
                        className={classNames(
                          "absolute left-[-31px] h-[23px] w-[23px] rounded-full text-center",
                          {
                            "text-[16px] tracking-tighter": item.mark === 10,
                            "bg-pink-600 text-white":
                              item.mark >= 9 && item.mark <= 10,
                            "bg-indigo-600 text-white":
                              item.mark >= 7 && item.mark <= 8,
                            "bg-zinc-300 text-zinc-950": item.mark <= 6,
                          },
                        )}
                      >
                        {item.mark}
                      </span>
                    ) : null}
                    <a
                      className="text-indigo-800 transition-colors hover:text-indigo-500 hover:transition-none"
                      href={
                        item.movie.slug
                          ? router.stringify("/movies/:movieSlug", {
                              movieSlug: item.movie.slug,
                            })
                          : router.stringify(
                              "/movies/by-kinopoisk-id/:kinopoiskId",
                              {
                                kinopoiskId: item.movie.kinopoiskId,
                              },
                            )
                      }
                    >
                      {item.movie.title}
                    </a>
                    {" "}·{" "}
                    <Join delimiter=", ">
                      {item.movie.directors.map((d) => d.fullName).join(", ") ||
                        null}
                      {item.movie.year}
                    </Join>
                    {" "}
                    <span className="relative -top-0.5 inline-flex space-x-1.5 text-xs text-zinc-500">
                      {item.movie.topPositionAllTime ? (
                        <span>№&nbsp;{item.movie.topPositionAllTime}</span>
                      ) : null}
                      {item.movie.bestMarksPercentage ? (
                        <span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="mr-px inline h-[0.8lh] w-[0.8lh]"
                          >
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" />
                          </svg>
                          {item.movie.bestMarksPercentage}%
                        </span>
                      ) : null}
                      {item.movie.goodMarksPercentage != null ? (
                        <span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="mr-px inline h-[0.8lh] w-[0.8lh]"
                          >
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M7 11v8a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1v-7a1 1 0 0 1 1 -1h3a4 4 0 0 0 4 -4v-1a2 2 0 0 1 4 0v5h3a2 2 0 0 1 2 2l-1 5a2 3 0 0 1 -2 2h-7a3 3 0 0 1 -3 -3" />
                          </svg>
                          {item.movie.goodMarksPercentage}%
                        </span>
                      ) : null}
                    </span>
                  </li>
                ))}
              </ol>
            </React.Fragment>
          ))}
        </div>
      </div>
    </main>
  );
};

export default ProfilePageByDecades;
