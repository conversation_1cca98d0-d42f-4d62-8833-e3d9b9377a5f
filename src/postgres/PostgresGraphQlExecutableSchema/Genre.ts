/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { GraphQLUnionTypeExtensions } from "graphql";

import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";

const Genre: graphql.GenreResolvers<PostgresGraphQlResolverContext> & {
  __extensions?: GraphQLUnionTypeExtensions;
} = {
  __extensions: {
    preloadBatch: async (
      parents: graphql.User[],
      context: PostgresGraphQlResolverContext,
    ): Promise<graphql.User[]> => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversParentTypes["User"]
        >(sql`
            SELECT
              graphql_genre.id::text as "id",
              graphql_genre.label as "label",
              graphql_genre.slug as "slug"
            FROM
              graphql_genre
            WHERE
              graphql_genre.id IN (${sql.raw(
                parents.map((parent) => Number(parent.id)).join(", "),
              )})
          `);

        const rowById = new Map(rows.map((row) => [row.id, row]));

        return parents.map((parent) => {
          const row = rowById.get(parent.id);

          assert(row, `Cannot find Genre with id "${parent.id}"`);

          return row;
        });
      });
    },
  },
};

export default Genre;
