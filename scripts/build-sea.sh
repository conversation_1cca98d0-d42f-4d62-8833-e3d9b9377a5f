#!/bin/bash
# Build Single Executable Application (SEA) for Linux
# Usage: ./build-sea.sh <node_version> <arch> <js_file> [output_name]

set -euo pipefail

[ $# -lt 3 ] && { echo "Usage: $0 <node_version> <arch> <js_file> [output_name]"; exit 1; }
[ ! -f "$3" ] && { echo "Error: JavaScript file not found: $3"; exit 1; }

temp_dir=$(mktemp -d)
trap 'rm -rf "$temp_dir"' EXIT

output_name="${4:-$(basename "$3" .js)}"
node_archive="node-$1-linux-$2.tar.gz"

# Create cache directory
cache_dir="$HOME/.cache/build-sea"
mkdir -p "$cache_dir"

# Check if Node.js binary is already cached
cached_node_binary="$cache_dir/node-$1-linux-$2"
if [ -f "$cached_node_binary" ]; then
    echo "Using cached Node.js $1 ($2)..."
    node_binary="$cached_node_binary"
else
    echo "Downloading Node.js $1 ($2)..."
    curl -fsSL "https://nodejs.org/dist/$1/$node_archive" -o "$temp_dir/$node_archive" || {
        echo "Error: Failed to download Node.js"; exit 1;
    }

    echo "Extracting..."
    tar -xzf "$temp_dir/$node_archive" -C "$temp_dir"
    extracted_node_binary="$temp_dir/node-$1-linux-$2/bin/node"
    chmod +x "$extracted_node_binary"

    [ ! -f "$extracted_node_binary" ] && { echo "Error: Node binary not found"; exit 1; }

    # Cache the binary for future use
    echo "Caching Node.js binary..."
    cp "$extracted_node_binary" "$cached_node_binary"
    node_binary="$cached_node_binary"
fi

command -v postject >/dev/null || npm install -g postject

cat > "$temp_dir/sea-config.json" << EOF
{
  "main": "$(realpath "$3")",
  "output": "$temp_dir/sea-prep.blob"
}
EOF

cp "$3" "$temp_dir/"

echo "Generating SEA blob..."
node --experimental-sea-config "$temp_dir/sea-config.json" || {
    echo "Error: Failed to generate SEA blob"; exit 1;
}

cp "$node_binary" "$temp_dir/node"

echo "Injecting SEA blob..."
postject "$temp_dir/node" NODE_SEA_BLOB "$temp_dir/sea-prep.blob" \
    --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2 || {
    echo "Error: Failed to inject SEA blob"; exit 1;
}

mv "$temp_dir/node" "$output_name"
chmod +x "$output_name"

echo "Success! Created: $output_name ($(du -h "$output_name" | cut -f1))"
