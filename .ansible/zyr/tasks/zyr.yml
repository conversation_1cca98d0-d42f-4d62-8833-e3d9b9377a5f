---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:web"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/zyr"
    dest: "{{ ansible_env.HOME }}/zyr"
    recursive: true
    use_ssh_args: true
- name: copy {{ nginx_static }}
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../src/web/dist/"
    dest: "{{ nginx_static }}/"
    delete: true
    recursive: true
    use_ssh_args: true
  become: true
- name: copy /etc/systemd/system/zyr.service
  ansible.builtin.template:
    src: zyr.service
    dest: /etc/systemd/system/zyr.service
  vars:
    binary_path: "{{ ansible_env.HOME }}/zyr"
    index_html_path: "{{ nginx_static }}/index.html"
    error_html_path: "{{ nginx_static }}/5xx.html"
  become: true
- name: start zyr.service
  ansible.builtin.systemd:
    name: zyr.service
    state: restarted
    enabled: true
    daemon_reload: true
  become: true
- name: copy /etc/nginx/sites-enabled/zyr.best
  ansible.builtin.template:
    src: zyr-nginx.conf
    dest: "/etc/nginx/sites-enabled/zyr.best"
  become: true
  notify:
    - restart nginx
- name: copy /etc/logrotate.d/nginx-zyr.best
  ansible.builtin.template:
    src: zyr-logrotate-nginx
    dest: "/etc/logrotate.d/nginx-zyr.best"
  become: true
  notify:
    - restart logrotate
- name: schedule health check
  ansible.builtin.cron:
    cron_file: zyr
    name: zyr-availability
    minute: "*"
    user: nikityy
    job: "curl -m 5 --retry 2 -o /dev/null https://zyr.best && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/63b0d019-8b51-4ceb-b157-81dc1e4fbfa8"
  become: true
- name: schedule backups
  ansible.builtin.cron:
    cron_file: zyr
    name: backup
    weekday: "6"
    hour: "0"
    minute: "0"
    user: nikityy
    job: "pg_dump -T wikipedia_article zyr > {{ postgres_backup_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/d39f7500-42e4-421c-a268-31a7cb90148f"
  become: true
